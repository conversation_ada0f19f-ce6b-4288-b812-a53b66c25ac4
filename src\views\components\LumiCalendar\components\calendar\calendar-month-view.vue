<template>
  <!--calendar month-view-->
  <div data-widget-item="calendar-inside">
    <!--calendar--header-->
    <div
      class="calendar--month-view--header grid grid-cols-repeat-7-minmax-0v1fr grid-flow-col w-full"
    >
      <!--day-column-cell-->
      <div
        class="select-none day-header w-full pt-1 px-2 text-left border-E0E0E0"
        v-for="(weekDayDate, weekindex) in weekDays"
        :class="{
          'border-r': weekindex !== weekDays.length - 1,
          'bg-FAFAFA': weekindex === weekDays.length - 1,
        }"
        :key="weekindex"
      >
        <!--dayname-->
        <span
          class="block text-71717A font-bold text-0dt625 leading-3 uppercase"
        >
          {{ dayName(weekDayDate, weekDayDate.getDate()).slice(0, -1) }}
        </span>
      </div>
    </div>
    <!--calendar--row-->
    <div
      class="calendar--month-view grid grid-cols-repeat-7-minmax-0v1fr grid-flow-col w-full"
      v-for="(line, index) in Math.ceil(monthDays.length / 7)"
      :key="index"
    >
      <!--day-row-cell-->
      <div
        class="relative select-none day-cell py-1 px-2 w-full min-h-5dt063 text-left border-b border-E0E0E0 calendar-cell-clickable"
        v-for="(monthDayDate, monthdayindex) in Array.from(monthDays).slice(
          index * 7,
          line * 7
        )"
        :class="{
          'border-r': monthdayindex !== 6,
          'bg-FAFAFA': monthdayindex === 6,
          'past-day-cell': isPastDay(monthDayDate)
        }"
        :key="monthdayindex"
        @click="handleCellClick(monthDayDate, $event)"
        @mouseenter="handleCellHover(monthDayDate, $event)"
        @mouseleave="hideHoverCard"
        @mousemove="updateHoverCardPosition($event)"
      >
        <!-- events are here -->
        <span
          class="block text-black font-medium text-1dt375 leading-8"
          :class="{
            'calendar--month-view-not-in---month':
              monthDayDate.getMonth() !== dateSelected.getMonth(),
            'calendar--month-view-actual-day':
              monthDayDate.getDate() === dateSelected.getDate() &&
              monthDayDate.getMonth() === dateSelected.getMonth(),
          }"
        >
          {{ monthDayDate.getDate() }}
        </span>
        <!-- event component -->
        <Events
          class="relative mt-1"
          :eventDate="monthDayDate"
          :slots="slots"
        />
        <!-- Overlay para hover -->
        <div class="calendar-cell-overlay"></div>
      </div>
    </div>

    <!-- Hover Card -->
    <CalendarHoverCard
      :visible="hoverCard.visible"
      :x="hoverCard.x"
      :y="hoverCard.y"
      :date="hoverCard.date"
      :time="hoverCard.time"
      :view="'month'"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import type { Slots } from "vue";

export interface Props {
  monthDays?: Date[];
  weekDays?: Date[];
  dateSelected: Date;
  slots: Slots;
}

import Events from "./calendar-event.vue";
import CalendarHoverCard from "./CalendarHoverCard.vue";
import {
  dayName,
} from "./common";

const props = withDefaults(defineProps<Props>(), {
  monthDays: () => [],
  weekDays: () => [],
});

// Estado do hover card
const hoverCard = reactive({
  visible: false,
  x: 0,
  y: 0,
  date: null as Date | null,
  time: null as string | null
});

// Verificar se é dia passado
const isPastDay = (date: Date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const targetDate = new Date(date);
  targetDate.setHours(0, 0, 0, 0);
  return targetDate < today;
};

// Métodos para lidar com cliques nas células
const handleCellClick = (date: Date, event?: MouseEvent) => {
  // Não permitir agendar para dias passados
  if (isPastDay(date)) {
    return;
  }

  // Não permitir clique se estiver sobre um evento existente
  if (event && isHoveringOverEvent(event)) {
    return;
  }

  // Emitir evento personalizado para o componente pai
  const customEvent = new CustomEvent('calendar:cell-clicked', {
    detail: {
      date: date,
      time: null, // Visualização mensal não tem horário específico
      view: 'month'
    },
    bubbles: true
  });
  document.dispatchEvent(customEvent);
};

// Verificar se o cursor está sobre um evento existente ou seus popups
const isHoveringOverEvent = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  // Verificar se o elemento clicado ou seus pais têm a classe de evento ou popup
  return target.closest('.calendar--event') !== null ||
         target.closest('.single-event-popup') !== null ||
         target.closest('.more-event-body') !== null ||
         target.closest('.calendar--action') !== null;
};

// Métodos para o hover card
const handleCellHover = (date: Date, event: MouseEvent) => {
  // SEMPRE verificar primeiro se estamos sobre um evento existente
  if (isHoveringOverEvent(event)) {
    hideHoverCard();
    return;
  }

  // Só mostrar hover card se estivermos em área vazia da célula
  hoverCard.visible = true;
  hoverCard.x = event.clientX;
  hoverCard.y = event.clientY;
  hoverCard.date = date;
  hoverCard.time = null; // Visualização mensal não tem horário específico
};

const hideHoverCard = () => {
  hoverCard.visible = false;
};

const updateHoverCardPosition = (event: MouseEvent) => {
  // Se estiver sobre um evento, esconder o hover card
  if (isHoveringOverEvent(event)) {
    hideHoverCard();
    return;
  }

  if (hoverCard.visible) {
    hoverCard.x = event.clientX;
    hoverCard.y = event.clientY;
  }
};
</script>

<style lang="scss" scoped>
.calendar--month-view-not-in---month {
  opacity: 0.5;
}

.calendar--month-view-actual-day {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  font-size: 16px;
  font-weight: bold;
  line-height: 18px;
  color: #fff;
  background: #0ea5e9;
  border-radius: 50%;
}

/* Estilos para células clicáveis */
.calendar-cell-clickable {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background-color: rgba(20, 112, 233, 0.05) !important;

    .calendar-cell-overlay {
      opacity: 1;
    }
  }

  .calendar-cell-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(20, 112, 233, 0.08) 0%, rgba(20, 112, 233, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    border-radius: 2px;
  }

  &:active {
    background-color: rgba(20, 112, 233, 0.1) !important;
    transform: scale(0.995);
  }

  &.past-day-cell {
    opacity: 0.6;

    &:hover {
      background-color: rgba(249, 115, 22, 0.05) !important;

      .calendar-cell-overlay {
        background: linear-gradient(135deg, rgba(249, 115, 22, 0.08) 0%, rgba(249, 115, 22, 0.02) 100%);
      }
    }

    &:active {
      background-color: rgba(249, 115, 22, 0.05) !important;
      transform: none;
    }

    // Permitir que eventos dentro da célula funcionem normalmente
    .calendar--event {
      cursor: pointer;
      opacity: 1;
      pointer-events: auto;
    }
  }
}
</style>
