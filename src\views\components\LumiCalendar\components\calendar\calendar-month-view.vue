<template>
  <!--calendar month-view-->
  <div data-widget-item="calendar-inside">
    <!--calendar--header-->
    <div
      class="calendar--month-view--header grid grid-cols-repeat-7-minmax-0v1fr grid-flow-col w-full"
    >
      <!--day-column-cell-->
      <div
        class="select-none day-header w-full pt-1 px-2 text-left border-E0E0E0"
        v-for="(weekDayDate, weekindex) in weekDays"
        :class="{
          'border-r': weekindex !== weekDays.length - 1,
          'bg-FAFAFA': weekindex === weekDays.length - 1,
        }"
        :key="weekindex"
      >
        <!--dayname-->
        <span
          class="block text-71717A font-bold text-0dt625 leading-3 uppercase"
        >
          {{ dayName(weekDayDate, weekDayDate.getDate()).slice(0, -1) }}
        </span>
      </div>
    </div>
    <!--calendar--row-->
    <div
      class="calendar--month-view grid grid-cols-repeat-7-minmax-0v1fr grid-flow-col w-full"
      v-for="(line, index) in Math.ceil(monthDays.length / 7)"
      :key="index"
    >
      <!--day-row-cell-->
      <div
        class="relative select-none day-cell py-1 px-2 w-full min-h-5dt063 text-left border-b border-E0E0E0 calendar-cell-clickable"
        v-for="(monthDayDate, monthdayindex) in Array.from(monthDays).slice(
          index * 7,
          line * 7
        )"
        :class="{
          'border-r': monthdayindex !== 6,
          'bg-FAFAFA': monthdayindex === 6,
        }"
        :key="monthdayindex"
        @click="handleCellClick(monthDayDate)"
        :title="`Agendar consulta para ${formatCellTitle(monthDayDate)}`"
      >
        <!-- events are here -->
        <span
          class="block text-black font-medium text-1dt375 leading-8"
          :class="{
            'calendar--month-view-not-in---month':
              monthDayDate.getMonth() !== dateSelected.getMonth(),
            'calendar--month-view-actual-day':
              monthDayDate.getDate() === dateSelected.getDate() &&
              monthDayDate.getMonth() === dateSelected.getMonth(),
          }"
        >
          {{ monthDayDate.getDate() }}
        </span>
        <!-- event component -->
        <Events
          class="relative mt-1"
          :eventDate="monthDayDate"
          :slots="slots"
        />
        <!-- Overlay para hover -->
        <div class="calendar-cell-overlay"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Slots } from "vue";

export interface Props {
  monthDays?: Date[];
  weekDays?: Date[];
  dateSelected: Date;
  slots: Slots;
}

import Events from "./calendar-event.vue";
import {
  twoDigit,
  incrementTime,
  fixDateTime,
  randomId,
  dayName,
  copyDate,
} from "./common";

const props = withDefaults(defineProps<Props>(), {
  monthDays: () => [],
  weekDays: () => [],
});

// Métodos para lidar com cliques nas células
const handleCellClick = (date: Date) => {
  // Emitir evento personalizado para o componente pai
  const event = new CustomEvent('calendar:cell-clicked', {
    detail: {
      date: date,
      time: null, // Visualização mensal não tem horário específico
      view: 'month'
    },
    bubbles: true
  });
  document.dispatchEvent(event);
};

const formatCellTitle = (date: Date) => {
  const dateStr = date.toLocaleDateString('pt-BR');
  return dateStr;
};
</script>

<style lang="scss" scoped>
.calendar--month-view-not-in---month {
  opacity: 0.5;
}

.calendar--month-view-actual-day {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  font-size: 16px;
  font-weight: bold;
  line-height: 18px;
  color: #fff;
  background: #0ea5e9;
  border-radius: 50%;
}

/* Estilos para células clicáveis */
.calendar-cell-clickable {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background-color: rgba(20, 112, 233, 0.05) !important;

    .calendar-cell-overlay {
      opacity: 1;
    }
  }

  .calendar-cell-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(20, 112, 233, 0.08) 0%, rgba(20, 112, 233, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    border-radius: 2px;
  }

  &:active {
    background-color: rgba(20, 112, 233, 0.1) !important;
    transform: scale(0.995);
  }
}
</style>
