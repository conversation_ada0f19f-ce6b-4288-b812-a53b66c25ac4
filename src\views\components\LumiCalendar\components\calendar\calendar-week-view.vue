<template>
  <!--calendar week-view-->
  <div data-widget-item="calendar-inside">
    <!--calendar--header-->
    <div
      class="calendar--week-view--header grid grid-cols-3-repeat-7-minmax-0v1fr-3rem grid-flow-col w-full sticky top-0 z-two"
    >
      <!--time-column-cell-->
      <div class="time-header" />
      <!--day-column-cell-->
      <div
        class="select-none day-header w-full pt-1 px-2 pb-4 text-left border-b border-E0E0E0 bg-white"
        v-for="(weekDayDate, weekindex) in weekDays"
        :class="{
          'border-r': weekindex !== weekDays.length - 1,
          weekindex: weekindex === weekDays.length - 1,
          selection: weekDayDate.getDate() === dateSelected.getDate(),
        }"
        :key="weekindex"
      >
        <!--dayname-->
        <span
          class="block text-71717A font-bold text-0dt625 leading-3 uppercase"
          :class="{
            'calendar--week-view-not-in---week':
              weekDayDate.getMonth() !== dateSelected.getMonth(),
          }"
        >
          {{ dayName(weekDayDate, weekDayDate.getDate()).slice(0, -1) }}
        </span>
        <!--daynumber-->
        <span
          class="block text-black font-medium text-1dt375 leading-8"
          :class="{
            'calendar--week-view-not-in---week':
              weekDayDate.getMonth() !== dateSelected.getMonth(),
          }"
        >
          {{ weekDayDate.getDate() }}
        </span>
      </div>
      <!--time-column-cell-->
      <div class="time-header" />
    </div>
    <!--calendar--row-->
    <div
      v-for="time in dayTimes"
      :key="time"
      class="calendar--week-view--row grid grid-cols-3-repeat-7-minmax-0v1fr-3rem grid-flow-col w-full"
    >
      <!--time-row-cell-->
      <div
        class="select-none time-cell text-left text-71717A font-medium text-xs pointer"
      >
        {{ timeFormat(time) }}
      </div>
      <!--day-row-cell-->
      <div
        class="relative select-none day-cell w-full text-left border-b border-E0E0E0 calendar-cell-clickable"
        v-for="(weekDayDate, weekindex) in weekDays"
        :class="{
          'border-r': weekindex !== weekDays.length - 1,
          weekindex: weekindex === weekDays.length - 1,
          selection: weekDayDate.getDate() === dateSelected.getDate(),
          'past-time-cell': isPastTime(weekDayDate, time)
        }"
        :key="weekindex"
        @click="handleCellClick(weekDayDate, time, $event)"
        @mouseenter="handleCellHover(weekDayDate, time, $event)"
        @mouseleave="hideHoverCard"
        @mousemove="updateHoverCardPosition($event)"
      >
        <!-- events are here -->
        <span class="block w-full min-h-2dt25 border-b border-F7F7F7" />
        <span class="block w-full min-h-2dt25" />
        <!-- event component -->
        <Events
          class="absolute top-0"
          :eventDate="weekDayDate"
          :eventTime="time"
          :slots="slots"
        />
        <!-- Overlay para hover -->
        <div class="calendar-cell-overlay"></div>
      </div>
      <!--time-row-cell-->
      <div
        class="time-cell select-none text-right text-71717A font-medium text-xs"
      >
        {{ timeFormat(time) }}
      </div>
    </div>

    <!-- Hover Card -->
    <CalendarHoverCard
      :visible="hoverCard.visible"
      :x="hoverCard.x"
      :y="hoverCard.y"
      :date="hoverCard.date"
      :time="hoverCard.time"
      :view="'week'"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import type { Slots } from "vue";

export interface Props {
  dayTimes?: string[];
  weekDays?: Date[];
  dateSelected: Date;
  slots: Slots;
}

import Events from "./calendar-event.vue";
import CalendarHoverCard from "./CalendarHoverCard.vue";
import {
  dayName,
  timeFormat,
} from "./common";

const props = withDefaults(defineProps<Props>(), {
  dayTimes: () => [],
  weekDays: () => [],
});

// Estado do hover card
const hoverCard = reactive({
  visible: false,
  x: 0,
  y: 0,
  date: null as Date | null,
  time: null as string | null
});

// Verificar se é horário passado
const isPastTime = (date: Date, time: string) => {
  const now = new Date();
  const targetDate = new Date(date);
  const [hours, minutes] = time.split(':').map(Number);
  targetDate.setHours(hours, minutes, 0, 0);
  return targetDate < now;
};

// Métodos para lidar com cliques nas células
const handleCellClick = (date: Date, time: string, event?: MouseEvent) => {
  // Não permitir agendar para horários passados
  if (isPastTime(date, time)) {
    return;
  }

  // Não permitir clique se estiver sobre um evento existente
  if (event && isHoveringOverEvent(event)) {
    return;
  }

  // Emitir evento personalizado para o componente pai
  const customEvent = new CustomEvent('calendar:cell-clicked', {
    detail: {
      date: date,
      time: time,
      view: 'week'
    },
    bubbles: true
  });
  document.dispatchEvent(customEvent);
};

// Verificar se o cursor está sobre um evento existente
const isHoveringOverEvent = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  // Verificar se o elemento clicado ou seus pais têm a classe de evento
  return target.closest('.calendar--event') !== null;
};

// Métodos para o hover card
const handleCellHover = (date: Date, time: string, event: MouseEvent) => {
  // Não mostrar hover card se estiver sobre um evento existente
  if (isHoveringOverEvent(event)) {
    hideHoverCard();
    return;
  }

  hoverCard.visible = true;
  hoverCard.x = event.clientX;
  hoverCard.y = event.clientY;
  hoverCard.date = date;
  hoverCard.time = time;
};

const hideHoverCard = () => {
  hoverCard.visible = false;
};

const updateHoverCardPosition = (event: MouseEvent) => {
  // Se estiver sobre um evento, esconder o hover card
  if (isHoveringOverEvent(event)) {
    hideHoverCard();
    return;
  }

  if (hoverCard.visible) {
    hoverCard.x = event.clientX;
    hoverCard.y = event.clientY;
  }
};
</script>

<style lang="scss" scoped>
.calendar--week-view-not-in---week {
  opacity: 0.5;
}
.calendar--week-view--header {
  .day-header {
    &.weekindex {
      background-color: #fafafa;
    }
    &.selection {
      background-color: #eff6ff;
    }
  }
}
.calendar--week-view--row {
  .time-cell {
    position: relative;
    transform: translateY(-0.5rem);
  }
  .day-cell {
    &.weekindex {
      background-color: #fafafa;
    }
    &.selection {
      background-color: #eff6ff;
    }
  }
}

/* Estilos para células clicáveis */
.calendar-cell-clickable {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background-color: rgba(20, 112, 233, 0.05) !important;

    .calendar-cell-overlay {
      opacity: 1;
    }
  }

  .calendar-cell-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(20, 112, 233, 0.08) 0%, rgba(20, 112, 233, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    border-radius: 2px;
  }

  &:active {
    background-color: rgba(20, 112, 233, 0.1) !important;
    transform: scale(0.995);
  }

  &.past-time-cell {
    cursor: not-allowed;
    opacity: 0.6;

    &:hover {
      background-color: rgba(249, 115, 22, 0.05) !important;

      .calendar-cell-overlay {
        background: linear-gradient(135deg, rgba(249, 115, 22, 0.08) 0%, rgba(249, 115, 22, 0.02) 100%);
      }
    }

    &:active {
      background-color: rgba(249, 115, 22, 0.05) !important;
      transform: none;
    }
  }
}
</style>
